
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { usePublishedContent } from '@/hooks/useContent';

// Dynamic data will be fetched from Supabase

const CaseStudyCard: React.FC<{ caseStudy: any }> = ({ caseStudy }) => {
  const getBadgeColor = (outcome_type: string) => {
    switch (outcome_type?.toLowerCase()) {
      case 'success': return 'bg-green-500/20 text-green-500';
      case 'failure': return 'bg-red-500/20 text-red-500';
      case 'mixed': return 'bg-yellow-500/20 text-yellow-500';
      default: return 'bg-blue-500/20 text-blue-500';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    if (currency === 'INR') {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <Badge variant="outline" className={getBadgeColor(caseStudy.outcome_type)}>
          {caseStudy.outcome_type?.charAt(0).toUpperCase() + caseStudy.outcome_type?.slice(1) || 'Success'} Case
        </Badge>
        <CardTitle className="text-xl mt-3">{caseStudy.title}</CardTitle>
        <CardDescription className="text-muted-foreground">
          {caseStudy.excerpt || caseStudy.content?.substring(0, 150) + '...'}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-muted-foreground mb-1">Investment</p>
            <p className="font-medium">
              {caseStudy.initial_investment
                ? formatCurrency(caseStudy.initial_investment, caseStudy.currency)
                : 'Various'
              }
            </p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground mb-1">Duration</p>
            <p className="font-medium">{caseStudy.investment_duration || 'Long-term'}</p>
          </div>
          <div className="col-span-2">
            <p className="text-xs text-muted-foreground mb-1">Outcome</p>
            <p className="font-medium">
              {caseStudy.return_percentage
                ? `${caseStudy.return_percentage}% return`
                : caseStudy.outcome_summary || 'Positive outcome'
              }
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Link to={`/case-studies/${caseStudy.id}`}>
          <Button className="w-full">Read Full Case Study</Button>
        </Link>
      </CardFooter>
    </Card>
  );
};

const CaseStudiesSection: React.FC = () => {
  const { data: allCaseStudies, isLoading, error } = usePublishedContent('case_studies', { limit: 3 });

  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[...Array(3)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-4 w-16 mb-3" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-4 w-full mt-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Skeleton className="h-3 w-16 mb-1" />
                <Skeleton className="h-4 w-20" />
              </div>
              <div>
                <Skeleton className="h-3 w-16 mb-1" />
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="col-span-2">
                <Skeleton className="h-3 w-16 mb-1" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  if (error) {
    return (
      <div className="container py-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Investment Case Studies</h2>
          <p className="text-muted-foreground">Unable to load case studies at the moment. Please try again later.</p>
        </div>
      </div>
    );
  }

  const caseStudies = allCaseStudies || [];

  return (
    <div className="container py-12">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold">Investment Case Studies</h2>
        <p className="text-muted-foreground mt-2">Real-world examples and stories explained in simple terms</p>
      </div>

      {isLoading ? (
        <LoadingSkeleton />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {caseStudies.map((caseStudy) => (
            <CaseStudyCard key={caseStudy.id} caseStudy={caseStudy} />
          ))}
        </div>
      )}

      <div className="mt-10 text-center">
        <Link to="/case-studies" className="text-primary hover:underline">
          View All Case Studies →
        </Link>
      </div>
    </div>
  );
};

export default CaseStudiesSection;
