import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface ScrollArrowProps {
  onClick: () => void;
}

const ScrollArrow: React.FC<ScrollArrowProps> = ({ onClick }) => {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    setIsClicked(true);
    onClick();
    
    // Reset animation state after animation completes
    setTimeout(() => {
      setIsClicked(false);
    }, 600);
  };

  return (
    <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
      <button
        onClick={handleClick}
        className={`
          group relative flex items-center justify-center
          w-12 h-12 rounded-full
          bg-background/80 backdrop-blur-sm
          border border-border/50
          hover:bg-background/90 hover:border-border
          transition-all duration-300 ease-out
          shadow-lg hover:shadow-xl
          ${isClicked ? 'animate-bounce' : 'float-animation hover:animate-pulse'}
        `}
        aria-label="Learn more about Syed Investments"
      >
        <ChevronDown 
          className={`
            h-6 w-6 text-muted-foreground
            group-hover:text-foreground
            transition-all duration-300 ease-out
            ${isClicked ? 'scale-110' : 'group-hover:scale-110'}
          `}
        />
        
        {/* Ripple effect on click */}
        {isClicked && (
          <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping" />
        )}
        
        {/* Subtle glow effect */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-b from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </button>
    </div>
  );
};

export default ScrollArrow;
