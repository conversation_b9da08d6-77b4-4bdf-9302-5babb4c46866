
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/contexts/AuthContext";
import { AuthModal } from "@/components/auth/AuthModal";
import { UserProfile } from "@/components/auth/UserProfile";
import { useIsAdmin } from "@/hooks/useContent";
import { Loader2, Settings, Menu, X } from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import logo from '/assets/logo.svg';

const Navbar: React.FC = () => {
  const { user, loading } = useAuth();
  const isAdmin = useIsAdmin();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navigationLinks = [
    { to: "/market-dashboard", label: "Market Dashboard" },
    { to: "/insights", label: "Insights" },
    { to: "/news", label: "News" },
    { to: "/education", label: "Education" },
    { to: "/case-studies", label: "Case Studies" },
    { to: "/about", label: "About Us" },
    // { to: "/feedback", label: "Feedback" },
  ];

  const closeMobileMenu = () => setMobileMenuOpen(false);

  // Close mobile menu on window resize to desktop size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [mobileMenuOpen]);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (mobileMenuOpen && !target.closest('[data-mobile-menu]')) {
        closeMobileMenu();
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [mobileMenuOpen]);

  return (
    <>
      <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border" data-mobile-menu>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link
                to="/"
                className="flex flex-col group transition-transform duration-200 hover:scale-105"
                onClick={closeMobileMenu}
              >
                <h1
                  className="text-xl font-bold tracking-tight flex flex-col items-start sm:items-center gap-0"
                  style={{ fontFamily: 'Zen Dots, monospace' }}
                >
                  <span className='text-2xl'>Syed's</span>
                  <span className="flex items-center gap-1">
                    <span className="text-s group-hover:text-muted-foreground/80 transition-colors duration-200">
                      Investments
                    </span>
                    <sup className="text-xs" style={{ fontFamily: 'Arial' }}>TM</sup>
                  </span>
                </h1>
              
                <p className="text-xs text-muted-foreground hidden sm:block group-hover:text-muted-foreground/80 transition-colors duration-200">
                  {/* Where vision meets valuation */}
                </p>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-1">
              {navigationLinks.map((link) => (
                <Link
                  key={link.to}
                  to={link.to}
                  className="relative px-4 py-2 text-sm font-medium text-muted-foreground
                             transition-all duration-200 hover:text-primary rounded-lg
                             hover:bg-accent/50 group"
                >
                  {link.label}
                  <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary scale-x-0
                                   group-hover:scale-x-100 transition-transform duration-200
                                   origin-center rounded-full"></span>
                </Link>
              ))}
            </nav>

            {/* Desktop Auth Buttons */}
            <div className="hidden md:flex items-center gap-3">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : user ? (
                <>
                  {/* <Button asChild variant="ghost" size="sm">
                    <Link to="/dashboard">Dashboard</Link>
                  </Button> */}
                  {isAdmin && (
                    <Button asChild variant="ghost" size="sm">
                      <Link to="/admin">
                        <Settings className="h-4 w-4 mr-2" />
                        Admin
                      </Link>
                    </Button>
                  )}
                  <UserProfile />
                </>
              ) : (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAuthModalOpen(true)}
                  >
                    Login
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setAuthModalOpen(true)}
                  >
                    Sign Up
                  </Button>
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center gap-2">
              {/* Mobile Auth - Show user profile or login button */}
              {user ? (
                <div className="scale-90">
                  <UserProfile />
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setAuthModalOpen(true)}
                  className="text-xs px-3 py-1.5 h-auto"
                >
                  Login
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 hover:bg-accent/50 transition-colors"
                aria-label="Toggle mobile menu"
                aria-expanded={mobileMenuOpen}
              >
                <div className="relative w-5 h-5">
                  <Menu
                    className={cn(
                      "absolute inset-0 h-5 w-5 transition-all duration-200",
                      mobileMenuOpen ? "opacity-0 rotate-90" : "opacity-100 rotate-0"
                    )}
                  />
                  <X
                    className={cn(
                      "absolute inset-0 h-5 w-5 transition-all duration-200",
                      mobileMenuOpen ? "opacity-100 rotate-0" : "opacity-0 -rotate-90"
                    )}
                  />
                </div>
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          className={cn(
            "md:hidden transition-all duration-300 ease-in-out overflow-hidden relative z-50",
            mobileMenuOpen
              ? "max-h-[32rem] opacity-100 border-t border-border shadow-lg"
              : "max-h-0 opacity-0"
          )}
          data-mobile-menu
        >
          <div className="px-4 py-6 space-y-1 bg-background border-b border-border">
            {navigationLinks.map((link, index) => (
              <Link
                key={link.to}
                to={link.to}
                onClick={closeMobileMenu}
                className={cn(
                  "block px-4 py-3 text-base font-medium text-muted-foreground",
                  "hover:text-primary hover:bg-accent/60 rounded-lg transition-all duration-200",
                  "transform hover:translate-x-1 active:scale-95"
                )}
                style={{
                  animationDelay: mobileMenuOpen ? `${index * 50}ms` : '0ms'
                }}
              >
                {link.label}
              </Link>
            ))}

            {/* Mobile Auth Section */}
            <div className="pt-6 mt-4 border-t border-border/50 space-y-3">
              {user ? (
                <>
                 
                  {isAdmin && (
                    <Link
                      to="/admin"
                      onClick={closeMobileMenu}
                      className="flex items-center px-4 py-3 text-base font-medium text-muted-foreground
                                 hover:text-primary hover:bg-accent/60 rounded-lg transition-all duration-200
                                 transform hover:translate-x-1 active:scale-95"
                    >
                      <Settings className="h-4 w-4 mr-3" />
                      Admin
                    </Link>
                  )}
                </>
              ) : (
                <div className="flex flex-col gap-3 px-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setAuthModalOpen(true);
                      closeMobileMenu();
                    }}
                    className="w-full h-11 rounded-lg font-medium border-2 hover:bg-accent/60 transition-all duration-200"
                  >
                    Login
                  </Button>
                  <Button
                    onClick={() => {
                      setAuthModalOpen(true);
                      closeMobileMenu();
                    }}
                    className="w-full h-11 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    Sign Up
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>


      </nav>

      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
      />
    </>
  );
};

export default Navbar;
