import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Database, AlertCircle, CheckCircle } from 'lucide-react';

interface PageData {
  id: string;
  slug: string;
  title: string;
  status: string;
  published_at: string | null;
  created_at: string;
}

const PageDebugger: React.FC = () => {
  const [pages, setPages] = useState<PageData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [aboutUsPage, setAboutUsPage] = useState<PageData | null>(null);

  const fetchPages = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching all pages...');
      
      // First, check if we can access the pages table at all
      const { data: allPages, error: allPagesError } = await supabase
        .from('pages')
        .select('id, slug, title, status, published_at, created_at')
        .order('created_at', { ascending: false });

      if (allPagesError) {
        console.error('Error fetching all pages:', allPagesError);
        setError(`Error fetching pages: ${allPagesError.message}`);
        return;
      }

      console.log('All pages:', allPages);
      setPages(allPages || []);

      // Now specifically try to fetch the about-us page
      const { data: aboutPage, error: aboutError } = await supabase
        .from('pages')
        .select('*')
        .eq('slug', 'about-us')
        .maybeSingle();

      if (aboutError) {
        console.error('Error fetching about-us page:', aboutError);
        setError(`Error fetching about-us page: ${aboutError.message}`);
      } else {
        console.log('About-us page:', aboutPage);
        setAboutUsPage(aboutPage);
      }

    } catch (err: any) {
      console.error('Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const createAboutUsPage = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setError('You must be logged in to create a page');
        return;
      }

      const { data, error } = await supabase
        .from('pages')
        .insert([
          {
            slug: 'about-us',
            title: 'About Syed Investments',
            content: `
              <h2>Welcome to Syed Investments</h2>
              <p>Where vision meets valuation. We are dedicated to empowering investors with accessible, accurate, and Shariah-compliant financial education.</p>
              <h3>Our Mission</h3>
              <p>To democratize financial education and provide every investor with the tools, knowledge, and insights needed to build sustainable wealth through informed investment decisions.</p>
              <h3>Our Vision</h3>
              <p>To become the leading platform for ethical and Shariah-compliant investment education, fostering a community of informed investors who create positive impact through their financial decisions.</p>
            `,
            meta_description: 'Learn about Syed Investments - your trusted partner for Shariah-compliant investment education and financial guidance.',
            author_id: user.id,
            status: 'published' as const,
            published_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Error creating about-us page:', error);
        setError(`Error creating page: ${error.message}`);
      } else {
        console.log('Created about-us page:', data);
        // Refresh the data
        await fetchPages();
      }
    } catch (err: any) {
      console.error('Unexpected error creating page:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPages();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Pages Table Debugger
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={fetchPages} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            {!aboutUsPage && (
              <Button onClick={createAboutUsPage} disabled={loading} variant="outline">
                Create About Us Page
              </Button>
            )}
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-red-800">{error}</span>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">About Us Page Status</CardTitle>
              </CardHeader>
              <CardContent>
                {aboutUsPage ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-green-800">Page exists</span>
                    </div>
                    <div><strong>Title:</strong> {aboutUsPage.title}</div>
                    <div><strong>Status:</strong> 
                      <Badge variant={aboutUsPage.status === 'published' ? 'default' : 'secondary'} className="ml-2">
                        {aboutUsPage.status}
                      </Badge>
                    </div>
                    <div><strong>Published:</strong> {aboutUsPage.published_at ? new Date(aboutUsPage.published_at).toLocaleDateString() : 'Not published'}</div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <span className="text-orange-800">About Us page not found</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">All Pages ({pages.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {pages.length > 0 ? (
                    pages.map((page) => (
                      <div key={page.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{page.title}</div>
                          <div className="text-sm text-gray-600">/{page.slug}</div>
                        </div>
                        <Badge variant={page.status === 'published' ? 'default' : 'secondary'}>
                          {page.status}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-gray-500">No pages found</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PageDebugger;
