/**
 * Yahoo Finance API Data Transformer
 * Transforms Yahoo Finance API response into chart-ready format
 */

export interface YahooFinanceResponse {
  result: Array<{
    meta: {
      currency: string;
      symbol: string;
      exchangeName: string;
      regularMarketPrice: number;
      previousClose: number;
      regularMarketDayHigh: number;
      regularMarketDayLow: number;
      regularMarketVolume: number;
      fiftyTwoWeekHigh: number;
      fiftyTwoWeekLow: number;
      longName: string;
      shortName: string;
    };
    timestamp: number[];
    indicators: {
      quote: Array<{
        open: number[];
        high: number[];
        low: number[];
        close: number[];
        volume: number[];
      }>;
    };
    events?: {
      dividends?: Record<string, { amount: number; date: number }>;
    };
  }>;
  error: null | string;
}

export interface ChartDataPoint {
  timestamp: number;
  date: string;
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  change: number;
  changePercent: number;
}

export interface TransformedChartData {
  data: ChartDataPoint[];
  meta: {
    symbol: string;
    currency: string;
    currentPrice: number;
    previousClose: number;
    dayHigh: number;
    dayLow: number;
    volume: number;
    fiftyTwoWeekHigh: number;
    fiftyTwoWeekLow: number;
    companyName: string;
  };
  summary: {
    totalPoints: number;
    timeRange: string;
    firstTimestamp: number;
    lastTimestamp: number;
  };
}

/**
 * Transform Yahoo Finance API response to chart-ready format
 */
export function transformYahooFinanceData(
  response: YahooFinanceResponse
): TransformedChartData | null {
  try {
    // Validate response structure
    if (!response?.result?.[0]) {
      console.error('Invalid Yahoo Finance response: missing result data');
      return null;
    }

    const result = response.result[0];
    const { meta, timestamp, indicators } = result;

    // Validate required data arrays
    if (!timestamp?.length || !indicators?.quote?.[0]) {
      console.error('Invalid Yahoo Finance response: missing timestamp or quote data');
      return null;
    }

    const quote = indicators.quote[0];
    const { open, high, low, close, volume } = quote;

    // Validate all OHLCV arrays have the same length
    const expectedLength = timestamp.length;
    if (
      !open?.length || open.length !== expectedLength ||
      !high?.length || high.length !== expectedLength ||
      !low?.length || low.length !== expectedLength ||
      !close?.length || close.length !== expectedLength ||
      !volume?.length || volume.length !== expectedLength
    ) {
      console.error('Invalid Yahoo Finance response: OHLCV arrays length mismatch');
      return null;
    }

    // Transform data points
    const data: ChartDataPoint[] = timestamp.map((ts, index) => {
      const currentClose = close[index];
      const previousClose = index > 0 ? close[index - 1] : meta.previousClose;
      const change = currentClose - previousClose;
      const changePercent = (change / previousClose) * 100;

      return {
        timestamp: ts,
        date: new Date(ts * 1000).toLocaleDateString(),
        time: new Date(ts * 1000).toLocaleTimeString(),
        open: parseFloat(open[index].toFixed(2)),
        high: parseFloat(high[index].toFixed(2)),
        low: parseFloat(low[index].toFixed(2)),
        close: parseFloat(currentClose.toFixed(2)),
        volume: volume[index],
        change: parseFloat(change.toFixed(2)),
        changePercent: parseFloat(changePercent.toFixed(2))
      };
    });

    // Filter out invalid data points (where any OHLC value is null/undefined)
    const validData = data.filter(point => 
      point.open > 0 && point.high > 0 && point.low > 0 && point.close > 0
    );

    return {
      data: validData,
      meta: {
        symbol: meta.symbol,
        currency: meta.currency,
        currentPrice: meta.regularMarketPrice,
        previousClose: meta.previousClose,
        dayHigh: meta.regularMarketDayHigh,
        dayLow: meta.regularMarketDayLow,
        volume: meta.regularMarketVolume,
        fiftyTwoWeekHigh: meta.fiftyTwoWeekHigh,
        fiftyTwoWeekLow: meta.fiftyTwoWeekLow,
        companyName: meta.longName || meta.shortName
      },
      summary: {
        totalPoints: validData.length,
        timeRange: `${validData[0]?.date} - ${validData[validData.length - 1]?.date}`,
        firstTimestamp: validData[0]?.timestamp || 0,
        lastTimestamp: validData[validData.length - 1]?.timestamp || 0
      }
    };

  } catch (error) {
    console.error('Error transforming Yahoo Finance data:', error);
    return null;
  }
}

/**
 * Create sample data for testing when API is unavailable
 */
export function createSampleChartData(): TransformedChartData {
  const now = Date.now();
  const data: ChartDataPoint[] = [];
  
  for (let i = 0; i < 100; i++) {
    const timestamp = Math.floor((now - (100 - i) * 60000) / 1000);
    const basePrice = 1900 + Math.sin(i * 0.1) * 50;
    const volatility = Math.random() * 10 - 5;
    
    data.push({
      timestamp,
      date: new Date(timestamp * 1000).toLocaleDateString(),
      time: new Date(timestamp * 1000).toLocaleTimeString(),
      open: parseFloat((basePrice + volatility).toFixed(2)),
      high: parseFloat((basePrice + volatility + Math.random() * 5).toFixed(2)),
      low: parseFloat((basePrice + volatility - Math.random() * 5).toFixed(2)),
      close: parseFloat((basePrice + volatility + Math.random() * 2 - 1).toFixed(2)),
      volume: Math.floor(Math.random() * 50000),
      change: parseFloat((Math.random() * 4 - 2).toFixed(2)),
      changePercent: parseFloat((Math.random() * 0.2 - 0.1).toFixed(2))
    });
  }

  return {
    data,
    meta: {
      symbol: 'SAMPLE.NS',
      currency: 'INR',
      currentPrice: 1900,
      previousClose: 1895,
      dayHigh: 1920,
      dayLow: 1880,
      volume: 1000000,
      fiftyTwoWeekHigh: 2000,
      fiftyTwoWeekLow: 1500,
      companyName: 'Sample Company Limited'
    },
    summary: {
      totalPoints: data.length,
      timeRange: `${data[0].date} - ${data[data.length - 1].date}`,
      firstTimestamp: data[0].timestamp,
      lastTimestamp: data[data.length - 1].timestamp
    }
  };
}

/**
 * Validate if Yahoo Finance response has required chart data
 */
export function validateYahooFinanceResponse(response: any): boolean {
  return !!(
    response?.result?.[0]?.timestamp?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.open?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.high?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.low?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.close?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.volume?.length
  );
}
