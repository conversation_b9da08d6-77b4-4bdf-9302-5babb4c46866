import React, { useState } from 'react';
import { usePublishedContent, useContentCategories } from '@/hooks/useContent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Calendar, User, TrendingUp, DollarSign, Clock, FileText } from 'lucide-react';
import { Link } from 'react-router-dom';

const CaseStudies: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  
  const { data: caseStudies, isLoading, error } = usePublishedContent('case_studies', {
    search: searchTerm,
    category: selectedCategory || undefined,
  });
  
  const { data: categories } = useContentCategories('case_studies');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(1)}%`;
  };

  const getReturnColor = (percentage: number) => {
    return percentage >= 0 ? 'text-green-600' : 'text-red-600';
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error Loading Case Studies</h1>
          <p className="text-muted-foreground">There was an error loading the case studies. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Investment Case Studies</h1>
        <p className="text-xl text-muted-foreground">
          Real-world investment examples and detailed analysis of successful strategies
        </p>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="mb-8 space-y-6">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-muted-foreground transition-colors duration-200" />
            </div>
            <Input
              placeholder="Search investment case studies, success stories, and analysis..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 pr-4 h-12 text-base bg-background/50 border-2 border-border/50 rounded-xl
                         focus:border-primary/50 focus:bg-background transition-all duration-200
                         placeholder:text-muted-foreground/70 shadow-sm hover:shadow-md focus:shadow-lg"
            />
            {searchTerm && (
              <button
                type="button"
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-4 flex items-center text-muted-foreground
                           hover:text-foreground transition-colors duration-200"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <Button
            type="submit"
            size="lg"
            className="px-8 h-12 rounded-xl font-medium shadow-sm hover:shadow-md transition-all duration-200"
          >
            Search
          </Button>
        </form>

        {/* Categories */}
        {categories && categories.length > 0 && (
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('')}
            >
              All Categories
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Content Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full mb-4" />
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : caseStudies && caseStudies.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {caseStudies.map((study) => (
            <Card key={study.id} className="hover:shadow-lg transition-shadow">
              {study.featured_image && (
                <div className="aspect-video overflow-hidden rounded-t-lg">
                  <img
                    src={study.featured_image}
                    alt={study.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  {study.category && (
                    <Badge variant="secondary">{study.category}</Badge>
                  )}
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    {new Date(study.published_at || study.created_at).toLocaleDateString()}
                  </div>
                </div>
                <CardTitle className="line-clamp-2">
                  <Link 
                    to={`/case-studies/${study.id}`}
                    className="hover:text-primary transition-colors"
                  >
                    {study.title}
                  </Link>
                </CardTitle>
                {study.excerpt && (
                  <CardDescription className="line-clamp-2">
                    {study.excerpt}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                {/* Investment Metrics */}
                <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                  {study.investment_amount && (
                    <div className="text-center">
                      <div className="flex items-center justify-center text-sm text-muted-foreground mb-1">
                        <DollarSign className="h-3 w-3 mr-1" />
                        Investment
                      </div>
                      <div className="font-semibold">
                        {formatCurrency(study.investment_amount)}
                      </div>
                    </div>
                  )}
                  {study.return_percentage !== null && (
                    <div className="text-center">
                      <div className="flex items-center justify-center text-sm text-muted-foreground mb-1">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Return
                      </div>
                      <div className={`font-semibold ${getReturnColor(study.return_percentage)}`}>
                        {formatPercentage(study.return_percentage)}
                      </div>
                    </div>
                  )}
                  {study.time_period && (
                    <div className="text-center col-span-2">
                      <div className="flex items-center justify-center text-sm text-muted-foreground mb-1">
                        <Clock className="h-3 w-3 mr-1" />
                        Time Period
                      </div>
                      <div className="font-semibold text-sm">
                        {study.time_period}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <User className="h-3 w-3 mr-1" />
                    Author
                  </div>
                  {study.tags && study.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {study.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {study.tags.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{study.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No case studies found</h3>
          <p className="text-muted-foreground">
            {searchTerm || selectedCategory
              ? 'Try adjusting your search criteria'
              : 'Check back later for new investment case studies'}
          </p>
        </div>
      )}

      {/* Performance Summary */}
      {caseStudies && caseStudies.length > 0 && (
        <div className="mt-12 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
          <h2 className="text-xl font-bold mb-4">Portfolio Performance Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {caseStudies.filter(s => s.return_percentage && s.return_percentage > 0).length}
              </div>
              <div className="text-sm text-muted-foreground">Profitable Cases</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {caseStudies.length}
              </div>
              <div className="text-sm text-muted-foreground">Total Case Studies</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {caseStudies.filter(s => s.return_percentage && s.return_percentage > 20).length}
              </div>
              <div className="text-sm text-muted-foreground">High Return Cases (20%)</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CaseStudies;
