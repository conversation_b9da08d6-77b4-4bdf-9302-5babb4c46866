import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  ArrowLeft, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  BarChart3,
  IndianRupee,
  Activity,
  Eye
} from 'lucide-react';
import { useQuarterlyOutlook } from '@/hooks/useContent';

interface QuarterlyOutlookData {
  id: string;
  title: string;
  quarter: string;
  year: number;
  market: 'us' | 'india';
  summary: string;
  key_metrics: {
    gdp_growth?: number;
    inflation_rate?: number;
    interest_rate?: number;
    market_outlook?: 'bullish' | 'bearish' | 'neutral';
    sector_highlights?: string[];
  };
  detailed_analysis: string;
  risk_factors: string[];
  opportunities: string[];
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
}

const IndianQuarterlyMarkets: React.FC = () => {
  const navigate = useNavigate();
  const [selectedOutlook, setSelectedOutlook] = useState<QuarterlyOutlookData | null>(null);
  
  // Fetch Indian market quarterly outlook data
  const { data: outlookResponse, isLoading, error } = useQuarterlyOutlook({ market: 'india' });
  const indianMarketData = outlookResponse || [];

  const getOutlookBadgeVariant = (outlook: string) => {
    switch (outlook) {
      case 'bullish': return 'default';
      case 'bearish': return 'destructive';
      case 'neutral': return 'secondary';
      default: return 'outline';
    }
  };

  const getOutlookIcon = (outlook: string) => {
    switch (outlook) {
      case 'bullish': return <TrendingUp className="h-4 w-4" />;
      case 'bearish': return <TrendingDown className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const handleViewDetails = (data: QuarterlyOutlookData) => {
    setSelectedOutlook(data);
  };

  const closeDetails = () => {
    setSelectedOutlook(null);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Data</h1>
          <p className="text-gray-600 mb-4">Unable to load quarterly market outlook data.</p>
          <Button onClick={() => navigate('/')}>Return Home</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => navigate('/')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-orange-500" />
            <h1 className="text-3xl font-bold">Indian Quarterly Market Outlook</h1>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="mb-8">
        <p className="text-lg text-muted-foreground max-w-3xl">
          Comprehensive quarterly analysis and forecasts for Indian markets, including NSE and BSE performance, 
          economic indicators, sector analysis, and investment opportunities in the Indian economy.
        </p>
      </div>

      {/* Quarterly Outlook Cards */}
      {indianMarketData.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Quarterly Outlook Available</h3>
            <p className="text-muted-foreground">
              Indian market quarterly outlook data will be published here by our analysts.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {indianMarketData.map((data) => (
            <Card key={data.id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl mb-2">{data.title}</CardTitle>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{data.quarter} {data.year}</span>
                    </div>
                  </div>
                  {data.key_metrics?.market_outlook && (
                    <Badge variant={getOutlookBadgeVariant(data.key_metrics.market_outlook)}>
                      <div className="flex items-center gap-1">
                        {getOutlookIcon(data.key_metrics.market_outlook)}
                        <span className="capitalize">{data.key_metrics.market_outlook}</span>
                      </div>
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  {data.summary}
                </p>
                
                {/* Key Metrics */}
                {data.key_metrics && (
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {data.key_metrics.gdp_growth && (
                      <div className="text-center p-2 bg-blue-50 rounded">
                        <div className="text-sm text-muted-foreground">GDP Growth</div>
                        <div className="font-semibold text-blue-600">{data.key_metrics.gdp_growth}%</div>
                      </div>
                    )}
                    {data.key_metrics.inflation_rate && (
                      <div className="text-center p-2 bg-orange-50 rounded">
                        <div className="text-sm text-muted-foreground">Inflation</div>
                        <div className="font-semibold text-orange-600">{data.key_metrics.inflation_rate}%</div>
                      </div>
                    )}
                    {data.key_metrics.interest_rate && (
                      <div className="text-center p-2 bg-green-50 rounded">
                        <div className="text-sm text-muted-foreground">Repo Rate</div>
                        <div className="font-semibold text-green-600">{data.key_metrics.interest_rate}%</div>
                      </div>
                    )}
                  </div>
                )}

                {/* Sector Highlights */}
                {data.key_metrics?.sector_highlights && data.key_metrics.sector_highlights.length > 0 && (
                  <div className="mb-4">
                    <div className="text-sm font-medium mb-2">Key Sectors:</div>
                    <div className="flex flex-wrap gap-1">
                      {data.key_metrics.sector_highlights.slice(0, 3).map((sector, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {sector}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Button 
                  onClick={() => handleViewDetails(data)}
                  className="w-full"
                  variant="outline"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Detailed Analysis
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Detailed View Modal */}
      {selectedOutlook && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h2 className="text-2xl font-bold mb-2">{selectedOutlook.title}</h2>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{selectedOutlook.quarter} {selectedOutlook.year}</span>
                    </div>
                    {selectedOutlook.key_metrics?.market_outlook && (
                      <Badge variant={getOutlookBadgeVariant(selectedOutlook.key_metrics.market_outlook)}>
                        <div className="flex items-center gap-1">
                          {getOutlookIcon(selectedOutlook.key_metrics.market_outlook)}
                          <span className="capitalize">{selectedOutlook.key_metrics.market_outlook}</span>
                        </div>
                      </Badge>
                    )}
                  </div>
                </div>
                <Button variant="ghost" onClick={closeDetails}>×</Button>
              </div>

              <div className="space-y-6">
                {/* Summary */}
                <div>
                  <h3 className="text-lg font-semibold mb-2">Executive Summary</h3>
                  <p className="text-muted-foreground">{selectedOutlook.summary}</p>
                </div>

                {/* Detailed Analysis */}
                <div>
                  <h3 className="text-lg font-semibold mb-2">Detailed Analysis</h3>
                  <div 
                    className="prose max-w-none text-muted-foreground"
                    dangerouslySetInnerHTML={{ __html: selectedOutlook.detailed_analysis }}
                  />
                </div>

                {/* Risk Factors */}
                {selectedOutlook.risk_factors && selectedOutlook.risk_factors.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-red-600">Risk Factors</h3>
                    <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                      {selectedOutlook.risk_factors.map((risk, index) => (
                        <li key={index}>{risk}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Opportunities */}
                {selectedOutlook.opportunities && selectedOutlook.opportunities.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-green-600">Investment Opportunities</h3>
                    <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                      {selectedOutlook.opportunities.map((opportunity, index) => (
                        <li key={index}>{opportunity}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IndianQuarterlyMarkets;
