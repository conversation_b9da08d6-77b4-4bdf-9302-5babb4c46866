
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Quill Editor Styles */
@import 'react-quill/dist/quill.snow.css';

/* Fix React Quill Editor Styling */
.ql-editor {
  min-height: 200px !important;
  background-color: white !important;
  color: black !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
}

.ql-toolbar {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-bottom: none !important;
  border-radius: 0.375rem 0.375rem 0 0 !important;
}

.ql-container {
  border: 1px solid #e2e8f0 !important;
  border-top: none !important;
  border-radius: 0 0 0.375rem 0.375rem !important;
}

.ql-toolbar .ql-stroke {
  fill: none !important;
  stroke: #374151 !important;
}

.ql-toolbar .ql-fill {
  fill: #374151 !important;
  stroke: none !important;
}

.ql-toolbar .ql-picker-label {
  color: #374151 !important;
}

.ql-toolbar button:hover {
  background-color: #e5e7eb !important;
}

.ql-toolbar button.ql-active {
  background-color: #3b82f6 !important;
  color: white !important;
}

.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: #374151 !important;
}

.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  background-color: white !important;
  border: 1px solid #e2e8f0 !important;
}

/* Dark mode compatibility */
.dark .ql-editor {
  background-color: #1f2937 !important;
  color: white !important;
  border-color: #374151 !important;
}

.dark .ql-toolbar {
  background-color: #111827 !important;
  border-color: #374151 !important;
}

.dark .ql-container {
  border-color: #374151 !important;
}

@layer base {
  :root {
    --background: 222 47% 9%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 32% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 212 26% 83%;

    --radius: 0.5rem;

    --market-up: 142 71% 45%;
    --market-down: 0 84% 60%;
    --market-neutral: 210 40% 98%;

    --success: 142 71% 45%;
    --danger: 0 84% 60%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', sans-serif;
  }
}

/* Custom classes */
@layer components {
  .stock-marquee {
    @apply whitespace-nowrap overflow-hidden py-2 bg-black text-white text-sm;
  }

  .marquee-content {
    @apply inline-block animate-marquee;
  }

  .marquee-content:hover {
    animation-play-state: paused;
  }

  .market-item {
    @apply px-4 inline-flex items-center space-x-2;
    min-width: fit-content;
    white-space: nowrap;
  }

  .stock-up {
    @apply text-market-up;
  }

  .stock-down {
    @apply text-market-down;
  }

  .stock-neutral {
    @apply text-market-neutral;
  }
}

/* Floating animation for scroll arrow */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Fade in up animation for hero elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  opacity: 0;
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Grid pattern background */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Floating animation for scroll arrow */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Splash screen animations */
@keyframes splashFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes splashSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes splashGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.splash-fade-in {
  animation: splashFadeIn 0.8s ease-out forwards;
}

.splash-slide-up {
  animation: splashSlideUp 0.6s ease-out forwards;
}

.splash-glow {
  animation: splashGlow 2s ease-in-out infinite;
}
