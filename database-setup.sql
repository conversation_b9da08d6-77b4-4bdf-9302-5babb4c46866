-- Finance Compass Database Setup Script
-- Run this entire script in your Supabase SQL Editor

-- 1. Create update function for timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Create user_profiles table
CREATE TABLE user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'editor')),
    full_name TEXT,
    bio TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create portfolios table
CREATE TABLE portfolios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    total_value DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create portfolio_holdings table
CREATE TABLE portfolio_holdings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    quantity DECIMAL(15,8) NOT NULL,
    average_cost DECIMAL(15,2) NOT NULL,
    current_price DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create insights table
CREATE TABLE insights (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create news table
CREATE TABLE news (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create education table
CREATE TABLE education (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_read_time INTEGER,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create case_studies table
CREATE TABLE case_studies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    investment_amount DECIMAL(15,2),
    return_percentage DECIMAL(5,2),
    time_period TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create pages table
CREATE TABLE pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    slug TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    meta_description TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Enable Row Level Security on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_holdings ENABLE ROW LEVEL SECURITY;
ALTER TABLE insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE news ENABLE ROW LEVEL SECURITY;
ALTER TABLE education ENABLE ROW LEVEL SECURITY;
ALTER TABLE case_studies ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;

-- 11. Create policies for user_profiles
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 12. Create policies for portfolios
CREATE POLICY "Users can view their own portfolios" ON portfolios
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own portfolios" ON portfolios
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own portfolios" ON portfolios
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own portfolios" ON portfolios
    FOR DELETE USING (auth.uid() = user_id);

-- 13. Create policies for portfolio_holdings
CREATE POLICY "Users can view holdings of their portfolios" ON portfolio_holdings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert holdings to their portfolios" ON portfolio_holdings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update holdings of their portfolios" ON portfolio_holdings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete holdings of their portfolios" ON portfolio_holdings
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM portfolios
            WHERE portfolios.id = portfolio_holdings.portfolio_id
            AND portfolios.user_id = auth.uid()
        )
    );

-- 14. Create content policies (insights, news, education, case_studies, pages)
-- Anyone can view published content, authors can manage their own content

-- Insights policies
CREATE POLICY "Anyone can view published insights" ON insights
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own insights" ON insights
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create insights" ON insights
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own insights" ON insights
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own insights" ON insights
    FOR DELETE USING (auth.uid() = author_id);

-- News policies
CREATE POLICY "Anyone can view published news" ON news
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own news" ON news
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create news" ON news
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own news" ON news
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own news" ON news
    FOR DELETE USING (auth.uid() = author_id);

-- Education policies
CREATE POLICY "Anyone can view published education" ON education
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own education" ON education
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create education" ON education
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own education" ON education
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own education" ON education
    FOR DELETE USING (auth.uid() = author_id);

-- Case studies policies
CREATE POLICY "Anyone can view published case_studies" ON case_studies
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own case_studies" ON case_studies
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create case_studies" ON case_studies
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own case_studies" ON case_studies
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own case_studies" ON case_studies
    FOR DELETE USING (auth.uid() = author_id);

-- Pages policies
CREATE POLICY "Anyone can view published pages" ON pages
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own pages" ON pages
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create pages" ON pages
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own pages" ON pages
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own pages" ON pages
    FOR DELETE USING (auth.uid() = author_id);

-- 15. Create triggers for automatic timestamp updates
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_portfolios_updated_at
    BEFORE UPDATE ON portfolios
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_portfolio_holdings_updated_at
    BEFORE UPDATE ON portfolio_holdings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_insights_updated_at
    BEFORE UPDATE ON insights
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_updated_at
    BEFORE UPDATE ON news
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_education_updated_at
    BEFORE UPDATE ON education
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_case_studies_updated_at
    BEFORE UPDATE ON case_studies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pages_updated_at
    BEFORE UPDATE ON pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 16. Insert sample About Us page
INSERT INTO pages (slug, title, content, author_id, status, published_at)
VALUES (
    'about-us',
    'About Syed Investments',
    '<h2>Welcome to Syed Investments</h2><p>Where vision meets valuation. We are dedicated to empowering investors with accessible, accurate, and Shariah-compliant financial education.</p>',
    (SELECT id FROM auth.users LIMIT 1),
    'published',
    NOW()
);

-- Setup complete!
-- Next step: Create your admin user profile by running the command below
-- with your actual user ID from the auth.users table

-- IMPORTANT: Replace 'YOUR_USER_ID_HERE' with your actual user ID
-- You can find your user ID by going to Authentication > Users in Supabase dashboard
--
-- INSERT INTO user_profiles (user_id, role, full_name)
-- VALUES ('YOUR_USER_ID_HERE', 'admin', 'Your Full Name');
